#!/usr/bin/env python3
"""
台湾学生录取通知邮件发送工具
Author: Kerwin
Date: 2025-07-20

功能：
1. 从第一批台生邮件信息.xlsx获取ksh、xm、邮箱
2. 根据ksh到tzs文件夹下找到对应的通知书pdf文件
3. 将通知书pdf文件和厦门理工学院2025级新生入学须知.pdf添加到邮件附件中
4. 发送录取通知邮件

使用方法：
1. 配置SMTP邮箱信息
2. 确保Excel文件包含：ksh、xm、邮箱、邮件发送类别
3. 运行：python send_taiwan_admission_notifications.py
"""

import pandas as pd
import sys
from pathlib import Path
from email_sender import EmailSender
import logging
from typing import List, Dict
import time
from datetime import datetime


def loadStudentData(excel_file_path: str) -> List[Dict]:
    """
    从Excel文件加载台湾学生数据

    @param excel_file_path: Excel文件路径
    @return: 学生信息列表
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file_path)

        students = []
        for _, row in df.iterrows():
            student = {
                "ksh": str(row["ksh"]).strip() if pd.notna(row["ksh"]) else None,
                "xm": str(row["xm"]).strip() if pd.notna(row["xm"]) else "同学",
                "email": str(row["邮箱"]).strip() if pd.notna(row["邮箱"]) else None,
                "mail_type": str(row["邮件发送类别"]).strip() if pd.notna(row["邮件发送类别"]) else "1",
            }

            # 只添加有邮箱地址和考生号的学生
            if student["email"] and student["ksh"] and student["email"] != "nan":
                students.append(student)

        return students

    except Exception as e:
        print(f"读取Excel文件时发生错误: {str(e)}")
        return []


def findNotificationPdf(ksh: str, tzs_folder: str) -> str:
    """
    根据考生号查找对应的通知书PDF文件

    @param ksh: 考生号
    @param tzs_folder: tzs文件夹路径
    @return: PDF文件路径，如果找不到返回None
    """
    tzs_path = Path(tzs_folder)

    # 直接查找以ksh命名的PDF文件
    pdf_file = tzs_path / f"{ksh}.pdf"
    if pdf_file.exists():
        return str(pdf_file)

    # 如果直接匹配找不到，尝试模糊匹配
    for pdf_file in tzs_path.glob("*.pdf"):
        if ksh in pdf_file.stem:
            return str(pdf_file)

    return None


def generateEmailContent(student_name: str, mail_type: str = "1") -> Dict:
    """
    生成邮件内容

    @param student_name: 学生姓名
    @param mail_type: 邮件发送类别，1=到校领取，2=后续寄出
    @return: 包含主题和正文的字典
    """
    subject = "厦门理工学院2025年本科生录取通知书"

    # 根据邮件发送类别设置不同的信息
    if mail_type == "2":
        other_info = "纸质版录取通知书后续寄出。"
    else:  # 默认为类别1
        other_info = "纸质版录取通知书可在报到之日到学校招生处领取。"

    body = f"""<html>
<body style="font-family: SimSun, serif;">
<p style="text-indent: 0em;">{student_name}同学：</p>
<p style="text-indent: 2em;">你好！</p>
<p style="text-indent: 2em;">恭喜你成为我校2025级新生。现将电子版录取通知书及新生入学须知发于你，请按通知书规定的时间到校报到。有关报到信息请详细阅读新生入学须知。</p>
<p style="text-indent: 2em;">{other_info}</p>
<p style="text-indent: 2em;">祝安！</p>
<div>
<p style="text-align: right;">厦门理工学院招生处</p>
<p style="text-align: right;">2025年7月20日</p>
</div>
</body>
</html>"""

    return {"subject": subject, "body": body}


def sendTaiwanNotifications(students: List[Dict], email_sender: EmailSender) -> Dict:
    """
    发送台湾学生录取通知邮件

    @param students: 学生信息列表
    @param email_sender: 邮件发送器
    @return: 发送结果统计
    """
    results = {"success": 0, "failed": 0, "total": len(students), "details": []}

    # 固定附件：新生入学须知
    admission_guide = "厦门理工学院2025级新生入学须知.pdf"
    if not Path(admission_guide).exists():
        print(f"⚠️  警告：找不到新生入学须知文件: {admission_guide}")
        admission_guide = None

    print(f"开始发送台湾学生录取通知邮件...")
    print(f"计划发送 {len(students)} 封邮件")
    print("=" * 60)

    for i, student in enumerate(students):
        print(f"\n📧 正在发送第 {i+1}/{len(students)} 封邮件")
        print(f"   收件人: {student['xm']}")
        print(f"   考生号: {student['ksh']}")
        print(f"   邮箱: {student['email']}")

        # 查找对应的通知书PDF
        notification_pdf = findNotificationPdf(student["ksh"], "tzs")

        if not notification_pdf:
            print(f"   ❌ 找不到考生号 {student['ksh']} 对应的通知书PDF文件")
            results["failed"] += 1
            results["details"].append({"student": student, "success": False, "error": "找不到通知书PDF"})
            continue

        # 准备附件列表
        attachments = []

        # 添加通知书PDF（使用绝对路径）
        notification_pdf_path = Path(notification_pdf).resolve()
        attachments.append(str(notification_pdf_path))
        print(f"   📎 通知书PDF: {notification_pdf_path.name}")

        # 添加新生入学须知（使用绝对路径）
        if admission_guide:
            admission_guide_path = Path(admission_guide).resolve()
            attachments.append(str(admission_guide_path))
            print(f"   📎 入学须知: {admission_guide_path.name}")

        # 生成邮件内容
        email_content = generateEmailContent(student["xm"], student.get("mail_type", "1"))

        # 发送邮件
        success = email_sender.sendEmail(
            to_emails=[student["email"]],
            subject=email_content["subject"],
            body=email_content["body"],
            is_html=True,
            attachments=attachments,
        )

        if success:
            results["success"] += 1
            print("   ✅ 发送成功")
        else:
            results["failed"] += 1
            print("   ❌ 发送失败")

        results["details"].append({"student": student, "success": success})

        # 发送间隔（避免频率限制）
        if i < len(students) - 1:
            print("   ⏳ 等待 3 秒后发送下一封...")
            time.sleep(12)

    return results


def queryStudentInfoByKsh(ksh: str, excel_file_path: str) -> Dict:
    """
    根据考生号从Excel文件查询学生信息

    @param ksh: 考生号
    @param excel_file_path: Excel文件路径
    @return: 包含姓名和邮件类别的字典，如果找不到返回None
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file_path)

        # 查找匹配的考生号
        for _, row in df.iterrows():
            if str(row["ksh"]).strip() == ksh:
                return {
                    "name": str(row["xm"]).strip() if pd.notna(row["xm"]) else None,
                    "mail_type": str(row["邮件发送类别"]).strip() if pd.notna(row["邮件发送类别"]) else "1",
                }

        return None
    except Exception as e:
        print(f"查询学生信息时发生错误: {str(e)}")
        return None


def testSendEmail():
    """
    测试邮件发送功能
    手动输入接收邮箱和考生号，使用考生号到第一批台生邮件信息.xlsx查询姓名和邮件类别
    """
    print("🧪 台湾学生录取通知邮件测试功能")
    print("Author: Kerwin | Date: 2025-07-20")
    print("=" * 60)

    # -------------- 1、输入测试信息 --------------------------
    print("\n📝 请输入测试信息：")

    test_email = input("   接收邮箱: ").strip()
    if not test_email:
        print("❌ 错误：接收邮箱不能为空")
        return

    test_ksh = input("   考生号: ").strip()
    if not test_ksh:
        print("❌ 错误：考生号不能为空")
        return

    # -------------- 2、从第一批台生邮件信息.xlsx查询学生信息 ----------------
    excel_file = "第一批台生邮件信息.xlsx"
    if not Path(excel_file).exists():
        print(f"❌ 错误：找不到Excel文件: {excel_file}")
        return

    student_info = queryStudentInfoByKsh(test_ksh, excel_file)
    if student_info and student_info["name"]:
        test_name = student_info["name"]
        test_mail_type = student_info["mail_type"]
        print(f"   ✅ 查询到学生姓名: {test_name}")
        print(f"   ✅ 邮件发送类别: {test_mail_type} ({'到校领取' if test_mail_type == '1' else '后续寄出'})")
    else:
        print(f"   ⚠️  警告：在第一批台生邮件信息中未找到考生号 {test_ksh} 对应的学生")
        test_name = input("   请手动输入学生姓名（默认为'测试'）: ").strip()
        if not test_name:
            test_name = "测试"
        test_mail_type = "1"  # 默认为类别1

    # -------------- 3、检查必要文件 --------------------------
    print(f"\n📁 检查必要文件：")

    # 检查tzs文件夹
    tzs_folder = Path("tzs")
    if not tzs_folder.exists():
        print(f"❌ 错误：找不到tzs文件夹")
        return

    # 查找对应的通知书PDF
    notification_pdf = findNotificationPdf(test_ksh, "tzs")
    if not notification_pdf:
        print(f"❌ 错误：找不到考生号 {test_ksh} 对应的通知书PDF文件")
        return

    print(f"   ✅ 通知书PDF: {Path(notification_pdf).name}")

    # 检查新生入学须知
    admission_guide = "厦门理工学院2025级新生入学须知.pdf"
    if Path(admission_guide).exists():
        print(f"   ✅ 新生入学须知: {admission_guide}")
    else:
        print(f"   ⚠️  新生入学须知: 文件不存在，将跳过此附件")
        admission_guide = None

    # -------------- 4、配置邮件发送器 ------------------------
    # 请根据实际情况修改以下配置
    SMTP_SERVER = "smtp.xmut.edu.cn"
    SMTP_PORT = 465
    USERNAME = "<EMAIL>"
    PASSWORD = "2kVWtUnMU3Cagqe9"

    # 检查是否为默认配置
    if USERNAME == "<EMAIL>" or PASSWORD == "your_app_password":
        print("\n⚠️  请先修改脚本中的邮箱配置信息！")
        print("需要配置：")
        print("  - SMTP_SERVER: SMTP服务器地址")
        print("  - SMTP_PORT: SMTP端口号")
        print("  - USERNAME: 您的邮箱地址")
        print("  - PASSWORD: 您的邮箱密码或应用专用密码")
        return

    # -------------- 5、创建邮件发送器 ------------------------
    try:
        email_sender = EmailSender(SMTP_SERVER, SMTP_PORT, USERNAME, PASSWORD, sender_name="厦门理工学院招生处")
        print(f"✅ 邮件发送器初始化成功")
    except Exception as e:
        print(f"❌ 邮件发送器初始化失败: {str(e)}")
        return

    # -------------- 6、发送测试邮件 --------------------------
    print(f"\n🚀 发送测试邮件...")
    print(f"   收件人: {test_name}")
    print(f"   考生号: {test_ksh}")
    print(f"   邮箱: {test_email}")

    # 准备附件列表
    attachments = []

    # 添加通知书PDF（使用绝对路径）
    notification_pdf_path = Path(notification_pdf).resolve()
    attachments.append(str(notification_pdf_path))

    # 添加新生入学须知（使用绝对路径）
    if admission_guide:
        admission_guide_path = Path(admission_guide).resolve()
        attachments.append(str(admission_guide_path))

    # 生成邮件内容
    email_content = generateEmailContent(test_name, test_mail_type)

    # 发送邮件
    success = email_sender.sendEmail(
        to_emails=[test_email],
        subject=email_content["subject"],
        body=email_content["body"],
        is_html=True,
        attachments=attachments,
    )

    # -------------- 7、显示发送结果 --------------------------
    print("\n" + "=" * 60)
    if success:
        print("✅ 测试邮件发送成功！")
        print(f"📧 邮件已发送到: {test_email}")
        print(f"📎 附件包含:")
        print(f"   - 通知书PDF: {Path(notification_pdf).name}")
        if admission_guide:
            print(f"   - 新生入学须知: {Path(admission_guide).name}")
    else:
        print("❌ 测试邮件发送失败！")
        print("请检查邮箱配置和网络连接")

    print(f"\n💡 提示:")
    print(f"   - 请检查目标邮箱确认是否收到邮件")
    print(f"   - 如果测试成功，可以运行批量发送功能")


def main():
    """
    主函数：执行台湾学生录取通知邮件发送
    """
    print("🏫 台湾学生录取通知邮件发送工具")
    print("Author: Kerwin | Date: 2025-07-20")
    print("=" * 60)

    # -------------- 1、加载学生数据 --------------------------
    excel_file = "第一批台生邮件信息.xlsx"

    if not Path(excel_file).exists():
        print(f"❌ 错误：找不到Excel文件: {excel_file}")
        sys.exit(1)

    students = loadStudentData(excel_file)
    print(f"📊 加载到 {len(students)} 名台湾学生信息")

    if not students:
        print("ℹ️  没有找到有效的学生信息")
        return

    # -------------- 2、显示学生详情 --------------------------
    print(f"\n📋 学生详情：")
    for i, student in enumerate(students[:5]):  # 只显示前5名
        print(f"   {i+1}. {student['xm']} (考生号: {student['ksh']}, 邮箱: {student['email']})")
    if len(students) > 5:
        print(f"   ... 还有 {len(students) - 5} 名学生")

    # -------------- 3、检查必要文件 --------------------------
    print(f"\n📁 检查必要文件：")

    # 检查tzs文件夹
    tzs_folder = Path("tzs")
    if not tzs_folder.exists():
        print(f"❌ 错误：找不到tzs文件夹")
        sys.exit(1)

    pdf_files = list(tzs_folder.glob("*.pdf"))
    print(f"   ✅ tzs文件夹: 找到 {len(pdf_files)} 个PDF文件")

    # 检查新生入学须知
    admission_guide = Path("厦门理工学院2025级新生入学须知.pdf")
    if admission_guide.exists():
        print(f"   ✅ 新生入学须知: {admission_guide.name}")
    else:
        print(f"   ⚠️  新生入学须知: 文件不存在，将跳过此附件")

    # 检查每个学生的通知书PDF
    missing_pdfs = []
    for student in students:
        pdf_path = findNotificationPdf(student["ksh"], "tzs")
        if not pdf_path:
            missing_pdfs.append(student["ksh"])

    if missing_pdfs:
        print(f"   ⚠️  缺少通知书PDF的考生号: {', '.join(missing_pdfs[:5])}")
        if len(missing_pdfs) > 5:
            print(f"       ... 还有 {len(missing_pdfs) - 5} 个")

        confirm = input(f"\n是否继续发送（将跳过缺少PDF的学生）？(y/N): ").strip().lower()
        if confirm != "y":
            print("操作已取消")
            return

    # -------------- 4、配置邮件发送器 ------------------------
    # 请根据实际情况修改以下配置
    SMTP_SERVER = "smtp.xmut.edu.cn"
    SMTP_PORT = 465
    USERNAME = "<EMAIL>"
    PASSWORD = "2kVWtUnMU3Cagqe9"

    # 检查是否为默认配置
    if USERNAME == "<EMAIL>" or PASSWORD == "your_app_password":
        print("\n⚠️  请先修改脚本中的邮箱配置信息！")
        print("需要配置：")
        print("  - SMTP_SERVER: SMTP服务器地址")
        print("  - SMTP_PORT: SMTP端口号")
        print("  - USERNAME: 您的邮箱地址")
        print("  - PASSWORD: 您的邮箱密码或应用专用密码")
        return

    # -------------- 5、创建邮件发送器 ------------------------
    try:
        email_sender = EmailSender(SMTP_SERVER, SMTP_PORT, USERNAME, PASSWORD, sender_name="厦门理工学院招生处")
        print(f"✅ 邮件发送器初始化成功")
    except Exception as e:
        print(f"❌ 邮件发送器初始化失败: {str(e)}")
        return

    # -------------- 6、发送邮件 ------------------------------
    print(f"\n🚀 开始发送邮件...")

    start_time = datetime.now()
    results = sendTaiwanNotifications(students, email_sender)
    end_time = datetime.now()

    # -------------- 7、显示发送结果 --------------------------
    actual_duration = end_time - start_time

    print("\n" + "=" * 60)
    print("📈 邮件发送完成！")
    print(f"📊 发送统计：")
    print(f"   总计学生: {results['total']} 名")
    print(f"   发送成功: {results['success']} 封")
    print(f"   发送失败: {results['failed']} 封")

    if len(results["details"]) > 0:
        success_rate = results["success"] / len(results["details"]) * 100
        print(f"   成功率: {success_rate:.1f}%")

    print(f"⏱️  实际耗时: {str(actual_duration).split('.')[0]}")

    if results["failed"] > 0:
        print(f"\n⚠️  发送失败的邮件:")
        for detail in results["details"]:
            if not detail["success"]:
                student = detail["student"]
                error = detail.get("error", "未知错误")
                print(f"   - {student['xm']} (考生号: {student['ksh']}, 邮箱: {student['email']}) - {error}")

    print(f"\n💡 提示:")
    print(f"   - 请检查邮件发送日志确认发送状态")
    print(f"   - 如有发送失败的邮件，可重新运行脚本")
    print(f"   - 建议在非工作时间发送，避免影响正常邮件收发")


if __name__ == "__main__":
    # 选择运行模式
    print("🏫 台湾学生录取通知邮件工具")
    print("Author: Kerwin | Date: 2025-07-20")
    print("=" * 60)
    print("请选择运行模式：")
    print("1. 测试模式 - 发送单封测试邮件")
    print("2. 批量模式 - 从Excel批量发送邮件")

    choice = input("\n请输入选择 (1 或 2): ").strip()

    if choice == "1":
        testSendEmail()
    elif choice == "2":
        main()
    else:
        print("❌ 错误：请输入正确的选择 (1 或 2)")
        sys.exit(1)
